import React, { useState } from 'react';

// مكونات الصفحات
const Dashboard = () => (
  <div style={{ padding: '20px' }}>
    <h2 style={{ color: '#1976d2', marginBottom: '20px' }}>📊 لوحة التحكم</h2>

    <div style={{
      display: 'grid',
      gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
      gap: '20px',
      marginBottom: '30px'
    }}>
      <div style={{
        backgroundColor: '#e3f2fd',
        padding: '20px',
        borderRadius: '12px',
        textAlign: 'center'
      }}>
        <h3 style={{ color: '#1976d2', fontSize: '2rem', margin: '0' }}>1,250</h3>
        <p style={{ margin: '5px 0 0 0', color: '#666' }}>إجمالي الإنتاج اليومي</p>
      </div>

      <div style={{
        backgroundColor: '#fff3e0',
        padding: '20px',
        borderRadius: '12px',
        textAlign: 'center'
      }}>
        <h3 style={{ color: '#ff9800', fontSize: '2rem', margin: '0' }}>45</h3>
        <p style={{ margin: '5px 0 0 0', color: '#666' }}>الطلبات النشطة</p>
      </div>

      <div style={{
        backgroundColor: '#f3e5f5',
        padding: '20px',
        borderRadius: '12px',
        textAlign: 'center'
      }}>
        <h3 style={{ color: '#9c27b0', fontSize: '2rem', margin: '0' }}>85%</h3>
        <p style={{ margin: '5px 0 0 0', color: '#666' }}>مستوى المخزون</p>
      </div>

      <div style={{
        backgroundColor: '#e8f5e8',
        padding: '20px',
        borderRadius: '12px',
        textAlign: 'center'
      }}>
        <h3 style={{ color: '#4caf50', fontSize: '2rem', margin: '0' }}>98%</h3>
        <p style={{ margin: '5px 0 0 0', color: '#666' }}>معدل الجودة</p>
      </div>
    </div>

    <div style={{
      backgroundColor: 'white',
      padding: '20px',
      borderRadius: '12px',
      boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
    }}>
      <h3 style={{ color: '#333', marginBottom: '15px' }}>📈 أداء الإنتاج الأسبوعي</h3>
      <div style={{
        height: '200px',
        backgroundColor: '#f8f9fa',
        borderRadius: '8px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: '#666'
      }}>
        رسم بياني لأداء الإنتاج (سيتم إضافة المخططات لاحقاً)
      </div>
    </div>
  </div>
);

const Production = () => (
  <div style={{ padding: '20px' }}>
    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
      <h2 style={{ color: '#ff9800', margin: 0 }}>🏭 إدارة الإنتاج</h2>
      <button style={{
        backgroundColor: '#ff9800',
        color: 'white',
        border: 'none',
        padding: '12px 24px',
        borderRadius: '8px',
        cursor: 'pointer',
        fontWeight: 'bold',
        fontSize: '14px'
      }}>
        + إضافة خط إنتاج جديد
      </button>
    </div>

    {/* إحصائيات الإنتاج */}
    <div style={{
      display: 'grid',
      gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
      gap: '15px',
      marginBottom: '30px'
    }}>
      <div style={{ backgroundColor: '#fff3e0', padding: '15px', borderRadius: '8px', textAlign: 'center' }}>
        <h3 style={{ color: '#ff9800', margin: '0 0 5px 0' }}>5</h3>
        <p style={{ margin: 0, fontSize: '14px' }}>خطوط الإنتاج النشطة</p>
      </div>
      <div style={{ backgroundColor: '#e8f5e8', padding: '15px', borderRadius: '8px', textAlign: 'center' }}>
        <h3 style={{ color: '#4caf50', margin: '0 0 5px 0' }}>1,250</h3>
        <p style={{ margin: 0, fontSize: '14px' }}>الإنتاج اليومي</p>
      </div>
      <div style={{ backgroundColor: '#ffebee', padding: '15px', borderRadius: '8px', textAlign: 'center' }}>
        <h3 style={{ color: '#f44336', margin: '0 0 5px 0' }}>2</h3>
        <p style={{ margin: 0, fontSize: '14px' }}>خطوط متوقفة</p>
      </div>
      <div style={{ backgroundColor: '#e3f2fd', padding: '15px', borderRadius: '8px', textAlign: 'center' }}>
        <h3 style={{ color: '#1976d2', margin: '0 0 5px 0' }}>85%</h3>
        <p style={{ margin: 0, fontSize: '14px' }}>كفاءة الإنتاج</p>
      </div>
    </div>

    {/* جدول خطوط الإنتاج */}
    <div style={{
      backgroundColor: 'white',
      borderRadius: '12px',
      boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
      overflow: 'hidden'
    }}>
      <div style={{ padding: '20px', borderBottom: '1px solid #eee' }}>
        <h3 style={{ margin: 0, color: '#333' }}>خطوط الإنتاج</h3>
      </div>

      <div style={{ overflowX: 'auto' }}>
        <table style={{ width: '100%', borderCollapse: 'collapse' }}>
          <thead>
            <tr style={{ backgroundColor: '#f8f9fa' }}>
              <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #dee2e6' }}>اسم الخط</th>
              <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #dee2e6' }}>المنتج</th>
              <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #dee2e6' }}>الحالة</th>
              <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #dee2e6' }}>الإنتاج/ساعة</th>
              <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #dee2e6' }}>الكفاءة</th>
              <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #dee2e6' }}>الإجراءات</th>
            </tr>
          </thead>
          <tbody>
            {[
              { name: 'خط الإنتاج A', product: 'منتج رقم 1', status: 'نشط', rate: '150', efficiency: '92%', statusColor: '#4caf50' },
              { name: 'خط الإنتاج B', product: 'منتج رقم 2', status: 'نشط', rate: '200', efficiency: '88%', statusColor: '#4caf50' },
              { name: 'خط الإنتاج C', product: 'منتج رقم 3', status: 'متوقف', rate: '0', efficiency: '0%', statusColor: '#f44336' },
              { name: 'خط الإنتاج D', product: 'منتج رقم 4', status: 'صيانة', rate: '0', efficiency: '0%', statusColor: '#ff9800' },
              { name: 'خط الإنتاج E', product: 'منتج رقم 5', status: 'نشط', rate: '180', efficiency: '95%', statusColor: '#4caf50' }
            ].map((line, index) => (
              <tr key={index} style={{ borderBottom: '1px solid #eee' }}>
                <td style={{ padding: '12px' }}>{line.name}</td>
                <td style={{ padding: '12px' }}>{line.product}</td>
                <td style={{ padding: '12px' }}>
                  <span style={{
                    backgroundColor: line.statusColor,
                    color: 'white',
                    padding: '4px 8px',
                    borderRadius: '4px',
                    fontSize: '12px'
                  }}>
                    {line.status}
                  </span>
                </td>
                <td style={{ padding: '12px' }}>{line.rate}</td>
                <td style={{ padding: '12px' }}>{line.efficiency}</td>
                <td style={{ padding: '12px' }}>
                  <button style={{
                    backgroundColor: '#1976d2',
                    color: 'white',
                    border: 'none',
                    padding: '6px 12px',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    marginLeft: '5px',
                    fontSize: '12px'
                  }}>
                    تعديل
                  </button>
                  <button style={{
                    backgroundColor: '#f44336',
                    color: 'white',
                    border: 'none',
                    padding: '6px 12px',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    fontSize: '12px'
                  }}>
                    حذف
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  </div>
);

const Inventory = () => (
  <div style={{ padding: '20px' }}>
    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
      <h2 style={{ color: '#9c27b0', margin: 0 }}>📦 إدارة المخزون</h2>
      <button style={{
        backgroundColor: '#9c27b0',
        color: 'white',
        border: 'none',
        padding: '12px 24px',
        borderRadius: '8px',
        cursor: 'pointer',
        fontWeight: 'bold',
        fontSize: '14px'
      }}>
        + إضافة مادة جديدة
      </button>
    </div>

    {/* إحصائيات المخزون */}
    <div style={{
      display: 'grid',
      gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
      gap: '15px',
      marginBottom: '30px'
    }}>
      <div style={{ backgroundColor: '#f3e5f5', padding: '15px', borderRadius: '8px', textAlign: 'center' }}>
        <h3 style={{ color: '#9c27b0', margin: '0 0 5px 0' }}>150</h3>
        <p style={{ margin: 0, fontSize: '14px' }}>إجمالي المواد</p>
      </div>
      <div style={{ backgroundColor: '#e8f5e8', padding: '15px', borderRadius: '8px', textAlign: 'center' }}>
        <h3 style={{ color: '#4caf50', margin: '0 0 5px 0' }}>85%</h3>
        <p style={{ margin: 0, fontSize: '14px' }}>مستوى المخزون</p>
      </div>
      <div style={{ backgroundColor: '#ffebee', padding: '15px', borderRadius: '8px', textAlign: 'center' }}>
        <h3 style={{ color: '#f44336', margin: '0 0 5px 0' }}>12</h3>
        <p style={{ margin: 0, fontSize: '14px' }}>مواد منخفضة</p>
      </div>
      <div style={{ backgroundColor: '#e3f2fd', padding: '15px', borderRadius: '8px', textAlign: 'center' }}>
        <h3 style={{ color: '#1976d2', margin: '0 0 5px 0' }}>$125,000</h3>
        <p style={{ margin: 0, fontSize: '14px' }}>قيمة المخزون</p>
      </div>
    </div>

    {/* جدول المخزون */}
    <div style={{
      backgroundColor: 'white',
      borderRadius: '12px',
      boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
      overflow: 'hidden'
    }}>
      <div style={{ padding: '20px', borderBottom: '1px solid #eee' }}>
        <h3 style={{ margin: 0, color: '#333' }}>قائمة المخزون</h3>
      </div>

      <div style={{ overflowX: 'auto' }}>
        <table style={{ width: '100%', borderCollapse: 'collapse' }}>
          <thead>
            <tr style={{ backgroundColor: '#f8f9fa' }}>
              <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #dee2e6' }}>اسم المادة</th>
              <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #dee2e6' }}>الفئة</th>
              <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #dee2e6' }}>الكمية</th>
              <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #dee2e6' }}>الحد الأدنى</th>
              <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #dee2e6' }}>السعر</th>
              <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #dee2e6' }}>الحالة</th>
              <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #dee2e6' }}>الإجراءات</th>
            </tr>
          </thead>
          <tbody>
            {[
              { name: 'مادة خام A', category: 'مواد أولية', quantity: '500', min: '100', price: '$25', status: 'متوفر', statusColor: '#4caf50' },
              { name: 'مادة خام B', category: 'مواد أولية', quantity: '75', min: '100', price: '$30', status: 'منخفض', statusColor: '#ff9800' },
              { name: 'مادة خام C', category: 'مواد كيميائية', quantity: '200', min: '50', price: '$45', status: 'متوفر', statusColor: '#4caf50' },
              { name: 'مادة خام D', category: 'مواد معدنية', quantity: '15', min: '25', price: '$60', status: 'نفذ', statusColor: '#f44336' },
              { name: 'مادة خام E', category: 'مواد بلاستيكية', quantity: '300', min: '80', price: '$20', status: 'متوفر', statusColor: '#4caf50' }
            ].map((item, index) => (
              <tr key={index} style={{ borderBottom: '1px solid #eee' }}>
                <td style={{ padding: '12px' }}>{item.name}</td>
                <td style={{ padding: '12px' }}>{item.category}</td>
                <td style={{ padding: '12px' }}>{item.quantity}</td>
                <td style={{ padding: '12px' }}>{item.min}</td>
                <td style={{ padding: '12px' }}>{item.price}</td>
                <td style={{ padding: '12px' }}>
                  <span style={{
                    backgroundColor: item.statusColor,
                    color: 'white',
                    padding: '4px 8px',
                    borderRadius: '4px',
                    fontSize: '12px'
                  }}>
                    {item.status}
                  </span>
                </td>
                <td style={{ padding: '12px' }}>
                  <button style={{
                    backgroundColor: '#1976d2',
                    color: 'white',
                    border: 'none',
                    padding: '6px 12px',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    marginLeft: '5px',
                    fontSize: '12px'
                  }}>
                    تعديل
                  </button>
                  <button style={{
                    backgroundColor: '#4caf50',
                    color: 'white',
                    border: 'none',
                    padding: '6px 12px',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    fontSize: '12px'
                  }}>
                    طلب
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  </div>
);

const Orders = () => (
  <div style={{ padding: '20px' }}>
    <h2 style={{ color: '#4caf50', marginBottom: '20px' }}>🛒 إدارة الطلبات</h2>
    <div style={{
      backgroundColor: 'white',
      padding: '20px',
      borderRadius: '12px',
      boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
    }}>
      <p>هنا ستجد جميع طلبات العملاء وحالة التنفيذ.</p>
    </div>
  </div>
);

function App() {
  const [currentPage, setCurrentPage] = useState('dashboard');

  const renderPage = () => {
    switch(currentPage) {
      case 'dashboard': return <Dashboard />;
      case 'production': return <Production />;
      case 'inventory': return <Inventory />;
      case 'orders': return <Orders />;
      default: return <Dashboard />;
    }
  };

  return (
    <div style={{
      padding: '20px',
      fontFamily: 'Arial, sans-serif',
      direction: 'rtl',
      backgroundColor: '#f0f0f0',
      minHeight: '100vh'
    }}>
      <h1 style={{
        color: '#1976d2',
        textAlign: 'center',
        fontSize: '2rem',
        marginBottom: '20px'
      }}>
        🏭 نظام التصنيع الاحترافي
      </h1>

      <div style={{
        backgroundColor: 'white',
        padding: '30px',
        borderRadius: '10px',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        textAlign: 'center',
        marginBottom: '20px'
      }}>
        <h2 style={{ color: '#4caf50' }}>✅ النظام يعمل بنجاح!</h2>
        <p>مرحباً بك في نظام إدارة التصنيع الشامل</p>
      </div>

      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '15px'
      }}>
        <div
          onClick={() => setCurrentPage('dashboard')}
          style={{
            backgroundColor: currentPage === 'dashboard' ? '#1976d2' : '#e3f2fd',
            color: currentPage === 'dashboard' ? 'white' : '#1976d2',
            padding: '20px',
            borderRadius: '8px',
            border: '2px solid #1976d2',
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            transform: currentPage === 'dashboard' ? 'scale(1.05)' : 'scale(1)'
          }}
        >
          <h3 style={{ margin: '0 0 10px 0' }}>📊 لوحة التحكم</h3>
          <p style={{ margin: 0 }}>مراقبة الأداء العام للمصنع</p>
        </div>

        <div
          onClick={() => setCurrentPage('production')}
          style={{
            backgroundColor: currentPage === 'production' ? '#ff9800' : '#fff3e0',
            color: currentPage === 'production' ? 'white' : '#ff9800',
            padding: '20px',
            borderRadius: '8px',
            border: '2px solid #ff9800',
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            transform: currentPage === 'production' ? 'scale(1.05)' : 'scale(1)'
          }}
        >
          <h3 style={{ margin: '0 0 10px 0' }}>🏭 الإنتاج</h3>
          <p style={{ margin: 0 }}>إدارة خطوط الإنتاج</p>
        </div>

        <div
          onClick={() => setCurrentPage('inventory')}
          style={{
            backgroundColor: currentPage === 'inventory' ? '#9c27b0' : '#f3e5f5',
            color: currentPage === 'inventory' ? 'white' : '#9c27b0',
            padding: '20px',
            borderRadius: '8px',
            border: '2px solid #9c27b0',
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            transform: currentPage === 'inventory' ? 'scale(1.05)' : 'scale(1)'
          }}
        >
          <h3 style={{ margin: '0 0 10px 0' }}>📦 المخزون</h3>
          <p style={{ margin: 0 }}>تتبع المواد والمنتجات</p>
        </div>

        <div
          onClick={() => setCurrentPage('orders')}
          style={{
            backgroundColor: currentPage === 'orders' ? '#4caf50' : '#e8f5e8',
            color: currentPage === 'orders' ? 'white' : '#4caf50',
            padding: '20px',
            borderRadius: '8px',
            border: '2px solid #4caf50',
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            transform: currentPage === 'orders' ? 'scale(1.05)' : 'scale(1)'
          }}
        >
          <h3 style={{ margin: '0 0 10px 0' }}>🛒 الطلبات</h3>
          <p style={{ margin: 0 }}>معالجة طلبات العملاء</p>
        </div>
      </div>

      {/* عرض المحتوى حسب الصفحة المختارة */}
      <div style={{ marginTop: '30px' }}>
        {renderPage()}
      </div>
    </div>
  );
}

export default App;
