import React, { useState } from 'react';

// مكونات الصفحات
const Dashboard = () => (
  <div style={{ padding: '20px' }}>
    <h2 style={{ color: '#1976d2', marginBottom: '20px' }}>📊 لوحة التحكم</h2>

    <div style={{
      display: 'grid',
      gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
      gap: '20px',
      marginBottom: '30px'
    }}>
      <div style={{
        backgroundColor: '#e3f2fd',
        padding: '20px',
        borderRadius: '12px',
        textAlign: 'center'
      }}>
        <h3 style={{ color: '#1976d2', fontSize: '2rem', margin: '0' }}>1,250</h3>
        <p style={{ margin: '5px 0 0 0', color: '#666' }}>إجمالي الإنتاج اليومي</p>
      </div>

      <div style={{
        backgroundColor: '#fff3e0',
        padding: '20px',
        borderRadius: '12px',
        textAlign: 'center'
      }}>
        <h3 style={{ color: '#ff9800', fontSize: '2rem', margin: '0' }}>45</h3>
        <p style={{ margin: '5px 0 0 0', color: '#666' }}>الطلبات النشطة</p>
      </div>

      <div style={{
        backgroundColor: '#f3e5f5',
        padding: '20px',
        borderRadius: '12px',
        textAlign: 'center'
      }}>
        <h3 style={{ color: '#9c27b0', fontSize: '2rem', margin: '0' }}>85%</h3>
        <p style={{ margin: '5px 0 0 0', color: '#666' }}>مستوى المخزون</p>
      </div>

      <div style={{
        backgroundColor: '#e8f5e8',
        padding: '20px',
        borderRadius: '12px',
        textAlign: 'center'
      }}>
        <h3 style={{ color: '#4caf50', fontSize: '2rem', margin: '0' }}>98%</h3>
        <p style={{ margin: '5px 0 0 0', color: '#666' }}>معدل الجودة</p>
      </div>
    </div>

    <div style={{
      backgroundColor: 'white',
      padding: '20px',
      borderRadius: '12px',
      boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
    }}>
      <h3 style={{ color: '#333', marginBottom: '15px' }}>📈 أداء الإنتاج الأسبوعي</h3>
      <div style={{
        height: '200px',
        backgroundColor: '#f8f9fa',
        borderRadius: '8px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: '#666'
      }}>
        رسم بياني لأداء الإنتاج (سيتم إضافة المخططات لاحقاً)
      </div>
    </div>
  </div>
);

const Production = () => (
  <div style={{ padding: '20px' }}>
    <h2 style={{ color: '#ff9800', marginBottom: '20px' }}>🏭 إدارة الإنتاج</h2>
    <div style={{
      backgroundColor: 'white',
      padding: '20px',
      borderRadius: '12px',
      boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
    }}>
      <p>هنا ستجد جميع معلومات خطوط الإنتاج والآلات والعمليات الجارية.</p>
    </div>
  </div>
);

const Inventory = () => (
  <div style={{ padding: '20px' }}>
    <h2 style={{ color: '#9c27b0', marginBottom: '20px' }}>📦 إدارة المخزون</h2>
    <div style={{
      backgroundColor: 'white',
      padding: '20px',
      borderRadius: '12px',
      boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
    }}>
      <p>هنا ستجد جميع معلومات المواد الخام والمنتجات النهائية.</p>
    </div>
  </div>
);

const Orders = () => (
  <div style={{ padding: '20px' }}>
    <h2 style={{ color: '#4caf50', marginBottom: '20px' }}>🛒 إدارة الطلبات</h2>
    <div style={{
      backgroundColor: 'white',
      padding: '20px',
      borderRadius: '12px',
      boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
    }}>
      <p>هنا ستجد جميع طلبات العملاء وحالة التنفيذ.</p>
    </div>
  </div>
);

function App() {
  const [currentPage, setCurrentPage] = useState('dashboard');

  const renderPage = () => {
    switch(currentPage) {
      case 'dashboard': return <Dashboard />;
      case 'production': return <Production />;
      case 'inventory': return <Inventory />;
      case 'orders': return <Orders />;
      default: return <Dashboard />;
    }
  };

  return (
    <div style={{
      padding: '20px',
      fontFamily: 'Arial, sans-serif',
      direction: 'rtl',
      backgroundColor: '#f0f0f0',
      minHeight: '100vh'
    }}>
      <h1 style={{
        color: '#1976d2',
        textAlign: 'center',
        fontSize: '2rem',
        marginBottom: '20px'
      }}>
        🏭 نظام التصنيع الاحترافي
      </h1>

      <div style={{
        backgroundColor: 'white',
        padding: '30px',
        borderRadius: '10px',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        textAlign: 'center',
        marginBottom: '20px'
      }}>
        <h2 style={{ color: '#4caf50' }}>✅ النظام يعمل بنجاح!</h2>
        <p>مرحباً بك في نظام إدارة التصنيع الشامل</p>
      </div>

      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '15px'
      }}>
        <div
          onClick={() => setCurrentPage('dashboard')}
          style={{
            backgroundColor: currentPage === 'dashboard' ? '#1976d2' : '#e3f2fd',
            color: currentPage === 'dashboard' ? 'white' : '#1976d2',
            padding: '20px',
            borderRadius: '8px',
            border: '2px solid #1976d2',
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            transform: currentPage === 'dashboard' ? 'scale(1.05)' : 'scale(1)'
          }}
        >
          <h3 style={{ margin: '0 0 10px 0' }}>📊 لوحة التحكم</h3>
          <p style={{ margin: 0 }}>مراقبة الأداء العام للمصنع</p>
        </div>

        <div
          onClick={() => setCurrentPage('production')}
          style={{
            backgroundColor: currentPage === 'production' ? '#ff9800' : '#fff3e0',
            color: currentPage === 'production' ? 'white' : '#ff9800',
            padding: '20px',
            borderRadius: '8px',
            border: '2px solid #ff9800',
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            transform: currentPage === 'production' ? 'scale(1.05)' : 'scale(1)'
          }}
        >
          <h3 style={{ margin: '0 0 10px 0' }}>🏭 الإنتاج</h3>
          <p style={{ margin: 0 }}>إدارة خطوط الإنتاج</p>
        </div>

        <div
          onClick={() => setCurrentPage('inventory')}
          style={{
            backgroundColor: currentPage === 'inventory' ? '#9c27b0' : '#f3e5f5',
            color: currentPage === 'inventory' ? 'white' : '#9c27b0',
            padding: '20px',
            borderRadius: '8px',
            border: '2px solid #9c27b0',
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            transform: currentPage === 'inventory' ? 'scale(1.05)' : 'scale(1)'
          }}
        >
          <h3 style={{ margin: '0 0 10px 0' }}>📦 المخزون</h3>
          <p style={{ margin: 0 }}>تتبع المواد والمنتجات</p>
        </div>

        <div
          onClick={() => setCurrentPage('orders')}
          style={{
            backgroundColor: currentPage === 'orders' ? '#4caf50' : '#e8f5e8',
            color: currentPage === 'orders' ? 'white' : '#4caf50',
            padding: '20px',
            borderRadius: '8px',
            border: '2px solid #4caf50',
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            transform: currentPage === 'orders' ? 'scale(1.05)' : 'scale(1)'
          }}
        >
          <h3 style={{ margin: '0 0 10px 0' }}>🛒 الطلبات</h3>
          <p style={{ margin: 0 }}>معالجة طلبات العملاء</p>
        </div>
      </div>
    </div>
  );
}

export default App;
