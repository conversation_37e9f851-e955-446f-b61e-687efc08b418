import React from 'react';

function App() {
  return (
    <div style={{
      padding: '20px',
      fontFamily: 'Arial, sans-serif',
      direction: 'rtl',
      backgroundColor: '#f0f0f0',
      minHeight: '100vh'
    }}>
      <h1 style={{
        color: '#1976d2',
        textAlign: 'center',
        fontSize: '2rem',
        marginBottom: '20px'
      }}>
        🏭 نظام التصنيع الاحترافي
      </h1>

      <div style={{
        backgroundColor: 'white',
        padding: '30px',
        borderRadius: '10px',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        textAlign: 'center',
        marginBottom: '20px'
      }}>
        <h2 style={{ color: '#4caf50' }}>✅ النظام يعمل بنجاح!</h2>
        <p>مرحباً بك في نظام إدارة التصنيع الشامل</p>
      </div>

      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '15px'
      }}>
        <div style={{
          backgroundColor: '#e3f2fd',
          padding: '20px',
          borderRadius: '8px',
          border: '2px solid #1976d2'
        }}>
          <h3 style={{ color: '#1976d2', margin: '0 0 10px 0' }}>📊 لوحة التحكم</h3>
          <p style={{ margin: 0 }}>مراقبة الأداء العام للمصنع</p>
        </div>

        <div style={{
          backgroundColor: '#fff3e0',
          padding: '20px',
          borderRadius: '8px',
          border: '2px solid #ff9800'
        }}>
          <h3 style={{ color: '#ff9800', margin: '0 0 10px 0' }}>🏭 الإنتاج</h3>
          <p style={{ margin: 0 }}>إدارة خطوط الإنتاج</p>
        </div>

        <div style={{
          backgroundColor: '#f3e5f5',
          padding: '20px',
          borderRadius: '8px',
          border: '2px solid #9c27b0'
        }}>
          <h3 style={{ color: '#9c27b0', margin: '0 0 10px 0' }}>📦 المخزون</h3>
          <p style={{ margin: 0 }}>تتبع المواد والمنتجات</p>
        </div>

        <div style={{
          backgroundColor: '#e8f5e8',
          padding: '20px',
          borderRadius: '8px',
          border: '2px solid #4caf50'
        }}>
          <h3 style={{ color: '#4caf50', margin: '0 0 10px 0' }}>🛒 الطلبات</h3>
          <p style={{ margin: 0 }}>معالجة طلبات العملاء</p>
        </div>
      </div>
    </div>
  );
}

export default App;
